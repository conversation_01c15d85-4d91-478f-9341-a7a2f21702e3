# This file is autogenerated.

# Copyright 2025 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")

source_set("build") {
  sources = [
    "//lynx/base/trace/android/src/main/jni/gen/TraceController_jni.h",
    "//lynx/base/trace/android/src/main/jni/gen/TraceController_register_jni.h",
    "//lynx/base/trace/android/src/main/jni/gen/TraceEvent_jni.h",
    "//lynx/base/trace/android/src/main/jni/gen/TraceEvent_register_jni.h",
    "//lynx/base/trace/android/src/main/jni/gen/lynx_trace_so_load.cc",
  ]
  configs += [
    "//lynx/base/trace/native:trace_private_config",
    "//lynx/base/trace/native:trace_public_config",
  ]
}
