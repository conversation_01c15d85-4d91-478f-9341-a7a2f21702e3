# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

interceptor_shared_sources = [
  "//lynx/core/runtime/bindings/jsi/interceptor/interceptor_factory.cc",
  "//lynx/core/runtime/bindings/jsi/interceptor/interceptor_factory.h",
]
network_interceptor_shared_sources = []

bindings_extend_sources = [
  "//lynx/core/runtime/bindings/jsi/system_info.cc",
  "//lynx/core/runtime/bindings/jsi/system_info.h",
]

jsbridge_extend_shared_sources = [
  "//lynx/core/runtime/bindings/jsi/modules/ios/lynx_module_interceptor.mm",
]

trace_extend_sources = [
  "//lynx/core/base/lynx_trace_categories.h",
  "//lynx/core/base/trace/trace_event_def.h",
]
