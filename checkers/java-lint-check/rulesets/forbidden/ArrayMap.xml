<?xml version="1.0"?>

<ruleset name="ArrayMap"
    xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>
        Check if ArrayMap is used
    </description>


    <!-- Your rules will come here -->
    <rule name="check ArrayMap"
        language="java"
        message="Do not use ArrayMap and its subtypes"
        class="net.sourceforge.pmd.lang.rule.XPathRule">
    <description>
        Prohibit the use of ArrayMap
    </description>
    <priority>1</priority>
    <properties>
        <property name="version" value="2.0"/>
        <property name="xpath">
            <value>
    <![CDATA[
    //ClassOrInterfaceType[pmd-java:typeIs("android.util.ArrayMap")]
    ]]>
            </value>
        </property>
    </properties>
    </rule>

    <rule name="check v4.ArrayMap"
        language="java"
        message="Do not use v4.ArrayMap and its subtypes"
        class="net.sourceforge.pmd.lang.rule.XPathRule">
    <description>
        Prohibit the use of ArrayMap
    </description>
    <priority>1</priority>
    <properties>
        <property name="version" value="2.0"/>
        <property name="xpath">
            <value>
    <![CDATA[
    //ClassOrInterfaceType[pmd-java:typeIs("android.util.support.v4.ArrayMap")]
    ]]>
            </value>
        </property>
    </properties>
    </rule>

    

</ruleset>