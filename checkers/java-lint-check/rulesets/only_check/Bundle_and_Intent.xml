<?xml version="1.0"?>

<ruleset name="Bundle_and_Intent"
    xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>
        Check if BaseBundle, Bundle or Intent is used
    </description>


    <!-- Your rules will come here -->

    <rule name="check Intent"
        language="java"
        message="Do not use Intent and its subtypes,because it uses ArrayMap"
        class="net.sourceforge.pmd.lang.rule.XPathRule">
    <description>
        Check if Intent is used and report
    </description>
    <priority>1</priority>
    <properties>
        <property name="version" value="2.0"/>
        <property name="xpath">
            <value>
    <![CDATA[
    //ClassOrInterfaceType[pmd-java:typeIs("android.content.Intent")]
    ]]>
            </value>
        </property>
    </properties>
    </rule>

    <rule name="check BaseBundle"
        language="java"
        message="Do not use BaseBundle and its subtypes,because it uses ArrayMap"
        class="net.sourceforge.pmd.lang.rule.XPathRule">
    <description>
        Check if BaseBundle is used and report
    </description>
    <priority>1</priority>
    <properties>
        <property name="version" value="2.0"/>
        <property name="xpath">
            <value>
    <![CDATA[
    //ClassOrInterfaceType[pmd-java:typeIs("android.os.BaseBundle")]
    ]]>
            </value>
        </property>
    </properties>
    </rule>

    <rule name="check Bundle"
        language="java"
        message="Do not use Bundle and its subtypes,because it uses ArrayMap"
        class="net.sourceforge.pmd.lang.rule.XPathRule">
    <description>
        Check if Bundle is used and report
    </description>
    <priority>1</priority>
    <properties>
        <property name="version" value="2.0"/>
        <property name="xpath">
            <value>
    <![CDATA[
    //ClassOrInterfaceType[pmd-java:typeIs("android.os.Bundle")]
    ]]>
            </value>
        </property>
    </properties>
    </rule>

</ruleset>