import os
import platform


machine = platform.machine().lower()
machine = "x86_64" if machine == "amd64" else machine
system = platform.system().lower()


deps = {
    'buildtools/clang-format': {
        "type": "http",
        "url": f"https://github.com/lynx-family/buildtools/releases/download/clang-format-020d2fb7/buildtools-clang-format-{system}-{machine}.tar.gz",
        "ignore_in_git": True,
        "condition": system in ['linux', 'darwin']
    },
    'buildtools/gn': {
        "type": "http",
        "url": f"https://github.com/lynx-family/buildtools/releases/download/gn-cc28efe6/buildtools-gn-{system}-{machine}.tar.gz",
        "ignore_in_git": True,
    },
    'buildtools/node': {
        "type": "http",
        "url": {
            "linux-x86_64": "https://nodejs.org/dist/v16.18.1/node-v16.18.1-linux-x64.tar.gz",
            "linux-arm64": "https://nodejs.org/dist/v16.18.1/node-v16.18.1-linux-arm64.tar.gz",
            "darwin-x86_64": "https://nodejs.org/dist/v16.18.1/node-v16.18.1-darwin-x64.tar.gz",
            "darwin-arm64": "https://nodejs.org/dist/v16.18.1/node-v16.18.1-darwin-arm64.tar.gz",
            "windows-x86_64": "https://nodejs.org/dist/v16.18.1/node-v16.18.1-win-x64.zip"
        }.get(f'{system}-{machine}', None),
        "sha256": {
            "linux-x86_64": "8949919fc52543efae3bfd057261927c616978614926682ad642915f98fe1981",
            "linux-arm64": "d6caa1439e8f3fbf4855b5cc1d09ae3eee31fc54ec29b7170603222ba6f8dfe6",
            "darwin-x86_64": "c190e106d4ac6177d1db3a5a739d39dd68bd276ba17f3d3c84039a93717e081e",
            "darwin-arm64": "71720bb0a80cf158d8fdf492def08048befd953ad45e2458b1d095e32c612ba7",
            "windows-x86_64": "db6a81de8e8ca3444495f1bcf04a883c076b4325d0fbaa032a190f88b38b30c5"
        }.get(f'{system}-{machine}', None),
        "ignore_in_git": True,
        "condition": system in ['linux', 'darwin', 'windows']
    },
    'buildtools/pmd': {
        "type": "http",
        "url": "https://github.com/pmd/pmd/releases/download/pmd_releases%2F6.48.0/pmd-bin-6.48.0.zip",
        "sha256": "0d7a2257902e9c319a81fabc0561c5720181bfd39d1a28f90c3bb138a2589c4e",
        "ignore_in_git": True,
        "condition": system in ['linux', 'darwin'],
    },
    'buildtools/checkstyle/checkstyle.jar': {
        "type": "http",
        "url": "https://github.com/checkstyle/checkstyle/releases/download/checkstyle-9.2.1/checkstyle-9.2.1-all.jar",
        "sha256": "12a66dfc4afd034da081385717ccb9c028d481313c9873c12ecf7f5cdb09daf6",
        "ignore_in_git": True,
        "decompress": False,
        "condition": system in ['linux', 'darwin'],
    }
}
