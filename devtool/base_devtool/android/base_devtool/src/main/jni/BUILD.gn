# This file is autogenerated.

# Copyright 2025 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/devtool/base_devtool/native/base_devtool.gni")

base_devtool_source_set("build") {
  sources = [
    "//lynx/devtool/base_devtool/android/base_devtool/src/main/jni/gen/DevToolGlobalSlot_jni.h",
    "//lynx/devtool/base_devtool/android/base_devtool/src/main/jni/gen/DevToolGlobalSlot_register_jni.h",
    "//lynx/devtool/base_devtool/android/base_devtool/src/main/jni/gen/DevToolSlot_jni.h",
    "//lynx/devtool/base_devtool/android/base_devtool/src/main/jni/gen/DevToolSlot_register_jni.h",
    "//lynx/devtool/base_devtool/android/base_devtool/src/main/jni/gen/lynx_base_devtool_so_load.cc",
  ]
}
