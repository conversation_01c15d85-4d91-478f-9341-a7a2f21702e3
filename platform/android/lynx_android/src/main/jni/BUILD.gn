# This file is autogenerated.

# Copyright 2025 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")

lynx_core_source_set("build") {
  sources = [
    "//lynx/platform/android/lynx_android/src/main/jni/gen/AttributeDescriptor_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/AttributeDescriptor_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/BlurUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/BlurUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/CallStackUtil_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/CallStackUtil_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/CallbackImpl_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/CallbackImpl_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ColorUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ColorUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/DeviceUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/DeviceUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/DisplayMetricsHolder_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/DisplayMetricsHolder_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/EnvUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/EnvUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/FluencySample_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/FluencySample_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/GradientUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/GradientUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ICURegister_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ICURegister_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ILynxJSIObjectDescriptor_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ILynxJSIObjectDescriptor_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JSProxy_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JSProxy_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JavaOnlyArray_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JavaOnlyArray_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JavaOnlyMap_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/JavaOnlyMap_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LLog_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LLog_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutContext_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutContext_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutNodeManager_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutNodeManager_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutNode_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LayoutNode_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxBackgroundRuntime_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxBackgroundRuntime_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEngineProxy_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEngineProxy_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEnv_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEnv_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxError_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxError_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEventReporter_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxEventReporter_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxFeatureCounter_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxFeatureCounter_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxGetUIResult_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxGetUIResult_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxJSIObjectHub_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxJSIObjectHub_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxLongTaskMonitor_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxLongTaskMonitor_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxModuleFactory_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxModuleFactory_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxModuleWrapper_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxModuleWrapper_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxNativeMemoryTracer_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxNativeMemoryTracer_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxResourceLoader_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxResourceLoader_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxTemplateRender_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxTemplateRender_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxUIRenderer_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxUIRenderer_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxWhiteBoard_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/LynxWhiteBoard_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/MethodDescriptor_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/MethodDescriptor_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/NativeFacadeReporter_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/NativeFacadeReporter_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/NativeFacade_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/NativeFacade_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PaintingContext_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PaintingContext_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PiperData_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PiperData_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PlatformCallBack_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PlatformCallBack_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PlatformExtraBundleHolder_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/PlatformExtraBundleHolder_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ReadableCompactArrayBuffer_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ReadableCompactArrayBuffer_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ReadableMapBuffer_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/ReadableMapBuffer_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/RuntimeLifecycleListenerDelegate_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/RuntimeLifecycleListenerDelegate_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TasmPlatformInvoker_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TasmPlatformInvoker_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TemplateBundle_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TemplateBundle_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TemplateData_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TemplateData_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TextUtils_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TextUtils_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TimingCollector_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/TimingCollector_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/VSyncMonitor_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/VSyncMonitor_register_jni.h",
    "//lynx/platform/android/lynx_android/src/main/jni/gen/lynx_so_load.cc",
  ]
}
